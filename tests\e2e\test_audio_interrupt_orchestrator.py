import asyncio
import os
import sys
from dotenv import load_dotenv

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from core.session_manager_v2 import SessionManagerV2
from utils.audio_utils import detect_voice_activity
from utils.audio_utils import record_microphone_audio_vad

load_dotenv()

# Set optimal interrupt detection environment variables for real-time demo
os.environ.setdefault('VAD_THRESHOLD', '0.05')  # Sensitive voice detection
os.environ.setdefault('VAD_METHOD', 'webrtcvad')  # Use WebRTC VAD for best performance
os.environ.setdefault('WEBRTC_AGGRESSIVENESS', '3')  # Maximum aggressiveness
os.environ.setdefault('TTS_INTERRUPT_COOLDOWN_SECONDS', '0.0')  # Immediate interrupts
os.environ.setdefault('SIMPLE_INTERRUPT_CONFIRMATION', 'true')  # Simplified confirmation for demo

async def select_input_device():
    """Select microphone input device for recording."""
    import sounddevice as sd
    devices = sd.query_devices()
    input_devices = [(i, d) for i, d in enumerate(devices) if d['max_input_channels'] > 0]
    print("Available input devices:")
    for idx, dev in input_devices:
        print(f"  [{idx}] {dev['name']} (inputs: {dev['max_input_channels']})")
    while True:
        try:
            device_index = int(input("Enter the device index for your microphone: ").strip())
            if any(idx == device_index for idx, _ in input_devices):
                return device_index
            else:
                print("Invalid index. Please select from the list above.")
        except Exception:
            print("Please enter a valid integer index.")

# async def record_microphone_audio(duration_sec=5, sample_rate=16000, device_index=None):
#     """Record audio from microphone using VAD for voice activity detection."""
#     import sounddevice as sd
#     import wave
#     import time

#     print(f"🎤 [MIC] Recording {duration_sec} seconds of audio...")
#     print("🗣️  Speak now!")

#     # Record audio from microphone
#     audio = sd.rec(int(duration_sec * sample_rate), samplerate=sample_rate, channels=1, dtype='int16', device=device_index)
#     sd.wait()

#     # Create timestamped filename
#     timestamp = int(time.time())
#     audio_filename = f'recorded_audio_{timestamp}.wav'

#     # Save audio to WAV file
#     audio_bytes = audio.tobytes()
#     with wave.open(audio_filename, 'wb') as wf:
#         wf.setnchannels(1)
#         wf.setsampwidth(2)
#         wf.setframerate(sample_rate)
#         wf.writeframes(audio_bytes)

#     # Use VAD to check if voice was detected
#     vad_result = detect_voice_activity(audio_bytes)
#     has_voice = vad_result.outputs.get("has_voice", False)

#     print(f"✅ [INFO] Saved recorded audio to: {audio_filename}")
#     print(f"🔍 [VAD] Voice detected: {has_voice}")

#     return audio_filename

async def get_audio_input_choice():
    """Get user choice for audio input method."""
    print("\n🎯 Audio Input Options:")
    print("1. Use microphone input (live recording)")
    print("2. Use specified audio file path")

    while True:
        try:
            choice = input("Enter your choice (1 or 2): ").strip()
            if choice in ['1', '2']:
                return choice
            else:
                print("Invalid choice. Please enter 1 or 2.")
        except Exception:
            print("Please enter a valid choice (1 or 2).")

async def run_turn_based_conversation_demo():
    """
    Run a production-ready turn-based conversation demo that behaves like commercial voice assistants.

    Features:
    - Initial workflow execution (Greeting → Inquiry → Banking Actions)
    - Post-greeting conversational mode (natural turn-based interaction)
    - Intent-based state routing (only transition when necessary)
    - Conversation context persistence
    - Real-time microphone input or file-based testing

    Flow:
    1. Greeting TTS (workflow mode)
    2. Wait for user input (conversational mode)
    3. Process: STT → Intent → Agent → TTS (stay in conversational mode)
    4. Repeat step 2-3 for natural conversation
    """
    print("🎯 Turn-Based Conversation Demo (Production-Ready)")
    print("=" * 60)
    print("🤖 This demo simulates a commercial voice assistant experience")
    print("   - Natural conversation flow after initial greeting")
    print("   - Intent-based state transitions only when needed")
    print("   - Persistent conversation context")
    print("   - Multiple conversation turns supported")
    print()

    # Get audio input choice
    choice = await get_audio_input_choice()

    audio_path = None
    device_index = None

    if choice == "1":
        # Option 1: Microphone input
        device_index = await select_input_device()
        print("✅ Microphone input selected")
    else:
        # Option 2: Audio file path
        while True:
            file_path = input("Enter path to audio file (.wav or .mp3): ").strip()
            if os.path.exists(file_path):
                audio_path = file_path
                print(f"✅ Audio file selected: {audio_path}")
                break
            else:
                print("❌ File not found. Please enter a valid path.")

    # Initialize SessionManagerV2
    session_manager = SessionManagerV2()
    workflow_name = 'banking_workflow_v2.json'
    user_id = 'streamlined_voice_user'

    try:
        # Create a new session
        session_id = await session_manager.create_session(workflow_name, user_id)
        print(f"✅ Session created: {session_id}")

        # Retrieve the memory manager for the session
        memory_manager = session_manager.active_sessions[session_id]["memory_manager"]

        # Initialize orchestrator
        orchestrator = await session_manager.initialize_orchestrator(session_id)

        print("\n🎭 Starting turn-based conversation demo...")
        print("📋 Flow: Initial Greeting → Conversational Mode → Multiple Turns")
        print()

        # Step 1: Initial Greeting (Workflow Mode)
        print("🤖 [STEP 1] Playing initial greeting...")
        print("   ⏳ Please wait for greeting to complete")
        result = await orchestrator.run()
        print(f"✅ [STEP 1] Greeting completed: {result.get('status', 'unknown')}")

        # Step 2: Enter Conversational Mode
        print("\n🔄 [STEP 2] Entering conversational mode...")
        print("   🎯 System is now ready for natural conversation")
        print("   💬 You can ask multiple questions, make requests, or have a conversation")
        print("   🛑 Type 'quit' or say 'goodbye' to end the conversation")
        print()

        conversation_turn = 1
        max_turns = 10  # Prevent infinite loops in demo

        while conversation_turn <= max_turns:
            print(f"\n💬 [TURN {conversation_turn}] Waiting for user input...")

            # Get user input for this turn
            if choice == "1":
                # Record from microphone
                print("   🗣️  Speak now...")
                try:
                    user_audio_path = await record_microphone_audio_vad(device_index=device_index)
                    print(f"   ✅ Recorded: {user_audio_path}")
                except Exception as e:
                    print(f"   ❌ Recording failed: {e}")
                    break
            else:
                # For file input, ask user to provide path for each turn
                print("   📁 Enter audio file path (or 'quit' to end):")
                file_input = input("   > ").strip()
                if file_input.lower() in ['quit', 'exit', 'goodbye']:
                    print("   👋 Ending conversation as requested")
                    break
                if not os.path.exists(file_input):
                    print("   ❌ File not found, ending conversation")
                    break
                user_audio_path = file_input
                print(f"   ✅ Using: {user_audio_path}")

            # Set user input for processing
            await memory_manager.set("contextual", "user_input_audio_path", user_audio_path)

            # Process the conversation turn
            print(f"   🤖 Processing turn {conversation_turn}...")
            print("      📝 STT → 🧠 Intent → ⚙️ Agent → 🔊 TTS")

            # Continue orchestrator execution for this turn
            turn_result = await orchestrator.run()

            print(f"   ✅ Turn {conversation_turn} completed: {turn_result.get('status', 'unknown')}")

            # Check if conversation should end
            if turn_result.get('status') == 'completed':
                print("   🏁 Conversation completed by system")
                break

            # Get conversation context for debugging
            conversation_context = await memory_manager.get("conversation_context")
            if conversation_context:
                print(f"   📊 Mode: {conversation_context.get('conversation_mode', 'unknown')}")
                print(f"   � Turn: {conversation_context.get('conversation_turn', 'unknown')}")

            conversation_turn += 1

            # Brief pause between turns
            await asyncio.sleep(1)

        print(f"\n🎉 Turn-based conversation demo completed!")
        print(f"   📊 Total turns: {conversation_turn - 1}")
        print(f"   🎯 Final status: {result.get('status', 'unknown')}")
        if result.get('reason'):
            print(f"   📝 Details: {result.get('reason')}")

    except Exception as e:
        print(f"❌ Error in workflow: {e}")
        import traceback
        traceback.print_exc()

    finally:

        # Clean up the session
        await session_manager.cleanup_session(session_id, reason="streamlined_test_complete")
        print("✅ Session cleaned up. Test complete!")

async def update_orchestrator_for_audio_retrieval():
    """
    Update the orchestrator to retrieve audio path from memory manager.
    This demonstrates the integration pattern you requested.
    """
    print("\n📋 Orchestrator Integration Pattern:")
    print("   The orchestrator should retrieve audio path using:")
    print("   audio_path = await self.memory_manager.get('audio_path')")
    print("   print(f'[DEBUG] STT step - checking for audio. self.memory_manager.get(\"audio_path\")')")
    print("   if audio_path:")
    print("       input_data = {'audio_path': audio_path}")
    print()
    print("   This pattern ensures the orchestrator gets audio from memory")
    print("   instead of hardcoded paths, enabling flexible audio input.")
    print()

if __name__ == "__main__":
    import sys
    import os
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))
    print("🚀 Starting Streamlined Voice Workflow Test")
    print("This test demonstrates a simplified single-flow voice pipeline with:")
    print("  ✅ Two audio input options (microphone or file)")
    print("  ✅ Complete STT → Intent → Agent → TTS workflow")
    print("  ✅ VAD integration for voice activity detection")
    print("  ✅ Memory manager audio path retrieval")
    print("  ✅ Proper greeting/ending state handling")
    print()

    # Show orchestrator integration pattern
    asyncio.run(update_orchestrator_for_audio_retrieval())

    # Run the streamlined test
    asyncio.run(run_streamlined_voice_workflow())